'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Navigation,
  getPrimaryNavigation,
  getSecondaryNavigation,
} from '@/components/layout/Navigation';
import { UserProfile } from '@/components/auth/UserProfile';
import { FamilySwitcher } from '@/components/family/FamilySwitcher';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useSidebar } from '@/contexts/SidebarContext';

interface SidebarProps {
  familyId?: string;
  user?: any;
  families?: any[];
  currentFamilyId?: string;
  className?: string;
}

export function Sidebar({
  familyId,
  user,
  families = [],
  currentFamilyId,
  className,
}: SidebarProps) {
  const { isCollapsed, toggleCollapsed } = useSidebar();
  const pathname = usePathname();

  return (
    <aside
      className={cn(
        'hidden lg:flex flex-col h-screen bg-white border-r border-gray-200',
        'fixed left-0 top-0 z-50',
        'transition-all duration-300 ease-in-out',
        isCollapsed ? 'w-16' : 'w-64',
        className
      )}
    >
      {/* Header */}
      <div className="h-18 flex items-center justify-between p-4 border-b border-gray-200">
        {!isCollapsed && (
          <Link href="/" className="flex items-center">
            <div className="text-lg font-bold font-display">
              <span style={{ color: 'var(--family-purple)' }}>FT</span>
              <span className="text-gray-900 ml-1">FamilyTasks</span>
            </div>
          </Link>
        )}

        <Button variant="ghost" size="sm" onClick={toggleCollapsed} className="p-2">
          {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>

      {/* User Section */}
      {/* {user && (
                <div className="p-4 border-b border-gray-200">
                    {!isCollapsed ? (
                        <UserProfile showName className="mb-3" />
                    ) : (
                        <div className="flex justify-center">
                            <UserProfile />
                        </div>
                    )}
                </div>
            )} */}

      {/* Family Context Section */}
      {user && families && families.length > 0 && (
        <div className="p-4 border-b border-gray-200 bg-gray-50/50">
          {!isCollapsed ? (
            <div>
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">
                Aktywna rodzina
              </div>
              <FamilySwitcher families={families} currentFamilyId={currentFamilyId} />
            </div>
          ) : (
            <div className="flex justify-center">
              <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                <span className="text-purple-600 text-sm font-semibold">
                  {families.find(f => f.family?.id === currentFamilyId)?.family?.name?.charAt(0) ||
                    'F'}
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          {/* Menu główne section */}
          {!isCollapsed && (
            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">
              Menu główne
            </div>
          )}
          <div className="space-y-1">
            <Navigation
              familyId={familyId}
              families={families}
              variant="sidebar"
              className="space-y-1"
              isCollapsed={isCollapsed}
            />
          </div>

          {/* Zarządzanie section */}
          {familyId && !isCollapsed && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">
                Zarządzanie
              </div>
              <div className="space-y-1">
                {getSecondaryNavigation(familyId).map(item => {
                  const isActive = () => {
                    if (item.href === '/') {
                      return pathname === '/';
                    }
                    
                    // Special handling for dashboard - exact match only
                    if (item.href.match(/^\/families\/[^\/]+$/)) {
                      return pathname === item.href;
                    }
                    
                    // For other paths, ensure we match the full segment to avoid partial matches
                    if (pathname === item.href) {
                      return true;
                    }
                    
                    // Check if pathname starts with href followed by a slash or end of string
                    return pathname.startsWith(item.href + '/') || pathname === item.href;
                  };

                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        'group relative flex items-center gap-2 rounded-lg transition-all duration-200',
                        'px-3 py-2 text-sm font-medium',
                        isActive()
                          ? 'text-purple-600 bg-purple-50 shadow-sm'
                          : 'text-gray-700 hover:text-purple-600 hover:bg-purple-50'
                      )}
                      title={item.description}
                    >
                      <item.icon className="h-4 w-4 shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="truncate">{item.name}</div>
                        {item.description && (
                          <div className="text-xs text-gray-500 truncate">{item.description}</div>
                        )}
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          )}

        </div>
      </div>
    </aside>
  );
}
